"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Search, ShoppingCart, MessageCircle, ArrowLeft, X } from 'lucide-react'

interface SellzioHeaderProps {
  onSearchFocus?: () => void
  onSearchBlur?: () => void
  onSearchChange?: (value: string) => void
  onSearchExecute?: (query: string) => void
  searchValue?: string
  isExpanded?: boolean
  onToggleExpanded?: () => void
}

export const SellzioHeader: React.FC<SellzioHeaderProps> = ({
  onSearchFocus,
  onSearchBlur,
  onSearchChange,
  onSearchExecute,
  searchValue = '',
  isExpanded = false,
  onToggleExpanded
}) => {
  const [inputValue, setInputValue] = useState(searchValue)
  const [showClearIcon, setShowClearIcon] = useState(false)
  const [placeholderIndex, setPlaceholderIndex] = useState(0)
  const inputRef = useRef<HTMLInputElement>(null)

  // Sync inputValue with searchValue prop when it changes
  useEffect(() => {
    setInputValue(searchValue)
    setShowClearIcon(searchValue.length > 0)
  }, [searchValue])

  // Placeholder texts sesuai docs/facet.html
  const placeholderTexts = [
    "Cari produk, brand, atau toko",
    "Sepatu sneakers pria",
    "Tas wanita branded",
    "Smartphone terbaru",
    "Laptop gaming murah",
    "Headphone wireless",
    "Jam tangan pria",
    "Dress wanita cantik",
    "Kamera mirrorless",
    "Power bank 10000mah",
    "Earbuds bluetooth",
    "Smartwatch fitness",
    "Keyboard mechanical",
    "Mouse gaming",
    "Blender portable"
  ]

  // Animasi placeholder
  useEffect(() => {
    if (!inputValue && !isExpanded) {
      const interval = setInterval(() => {
        setPlaceholderIndex((prev) => (prev + 1) % placeholderTexts.length)
      }, 3000)
      return () => clearInterval(interval)
    }
  }, [inputValue, isExpanded, placeholderTexts.length])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    setShowClearIcon(value.length > 0)
    onSearchChange?.(value)
  }

  const handleInputFocus = () => {
    onSearchFocus?.()
    onToggleExpanded?.()
  }

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Hanya trigger blur jika tidak dalam expanded mode
    if (!isExpanded) {
      onSearchBlur?.()
    }
  }

  const handleClearSearch = () => {
    setInputValue('')
    setShowClearIcon(false)
    onSearchChange?.('')
    inputRef.current?.focus()
  }

  const handleBackClick = () => {
    setInputValue('')
    setShowClearIcon(false)
    onSearchChange?.('')
    onToggleExpanded?.()
  }

  const handleSearchClick = () => {
    // Handle search action - execute search with current input value
    if (inputValue.trim()) {
      onSearchExecute?.(inputValue.trim())
      console.log('Search clicked:', inputValue)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Handle Enter key press - execute search
      if (inputValue.trim()) {
        onSearchExecute?.(inputValue.trim())
        console.log('Enter pressed:', inputValue)
      }
    }
  }

  if (isExpanded) {
    return (
      <div className="search-expanded">
        <div className="search-container">
          <button className="back-btn" onClick={handleBackClick}>
            <ArrowLeft size={20} />
          </button>
          <div className="search-input-wrapper">
            <input
              ref={inputRef}
              type="text"
              className="search-input"
              value={inputValue}
              onChange={handleInputChange}
              onBlur={handleInputBlur}
              onKeyPress={handleKeyPress}
              placeholder="Cari produk, brand, atau toko"
              autoFocus
            />
            {showClearIcon && (
              <button className="clear-search-icon" onClick={handleClearSearch}>
                <X size={12} />
              </button>
            )}
          </div>
          <button className="expanded-search-icon" onClick={handleSearchClick}>
            <Search size={16} />
          </button>
        </div>
      </div>
    )
  }

  return (
    <header className="header">
      <div className="search-container">
        <div className="search-input-wrapper">
          <input
            ref={inputRef}
            type="text"
            className="search-input"
            value={inputValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onKeyPress={handleKeyPress}
            placeholder=""
          />
          
          {/* Animated placeholder */}
          {!inputValue && !isExpanded && (
            <div className="search-placeholder">
              <div className="placeholder-static">
                Cari di Sellzio
              </div>
              <div className="placeholder-dynamic">
                {placeholderTexts.map((text, index) => (
                  <div
                    key={index}
                    className={`placeholder-text ${index === placeholderIndex ? 'active' : ''}`}
                    style={{
                      animationDelay: `${index * 3}s`
                    }}
                  >
                    {text}
                  </div>
                ))}
              </div>
            </div>
          )}

          {showClearIcon && (
            <button className="clear-search-icon" onClick={handleClearSearch}>
              <X size={12} />
            </button>
          )}
          
          <button className="search-icon" onClick={handleSearchClick}>
            <Search size={16} />
          </button>
        </div>

        {/* Header Icons */}
        <div className="header-icons">
          <div className="cart-icon">
            <ShoppingCart size={22} />
            <span className="cart-badge">3</span>
          </div>
          <div className="chat-icon">
            <MessageCircle size={22} />
            <div className="chat-dots">
              <div className="dot"></div>
              <div className="dot"></div>
              <div className="dot"></div>
            </div>
            <span className="chat-badge">2</span>
          </div>
        </div>
      </div>
    </header>
  )
}
