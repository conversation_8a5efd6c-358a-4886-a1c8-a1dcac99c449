"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showPredictions, setShowPredictions] = useState(false)
  const [predictions, setPredictions] = useState<any[]>([])
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearchResultShown, setIsSearchResultShown] = useState(false)

  // Function to load user interaction history from localStorage
  const loadUserInteractionHistory = () => {
    try {
      const history = localStorage.getItem('keywordPredictionHistory')
      if (history) {
        keywordPredictionDB.userInteractionHistory = JSON.parse(history)
      }
    } catch (e) {
      console.log('Failed to load prediction history from localStorage', e)
    }
  }

  // Initialize search history and prediction history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('searchHistory')
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory))
    } else {
      // Set default keywords jika tidak ada history
      const defaultKeywords = ['tas', 'divf', 'tas mata', 't', 'tas sekolah', 'tas selempang']
      setSearchHistory(defaultKeywords)
      localStorage.setItem('searchHistory', JSON.stringify(defaultKeywords))
    }

    // Load prediction history
    loadUserInteractionHistory()
  }, [])

  // Function to clear search history
  const clearSearchHistory = () => {
    setSearchHistory([])
    localStorage.setItem('searchHistory', JSON.stringify([]))
    console.log("Riwayat pencarian dihapus")
  }

  // Function to get initial keywords (first 6)
  const getInitialKeywords = () => {
    return searchHistory.slice(0, 6)
  }

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // Jangan auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)

    // Show predictions when typing (minimal 1 character) and search is expanded
    if (value.trim().length >= 1 && isSearchExpanded) {
      setShowPredictions(true)
      setShowSuggestions(false) // Hide suggestions when predictions are shown
      generatePredictions(value)
      document.body.classList.add('show-suggestions') // Use same class for overlay effect
    } else if (value.trim() === '' && isSearchExpanded) {
      // Show suggestions when input is empty
      setShowPredictions(false)
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    } else {
      setShowPredictions(false)
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setShowPredictions(false) // Reset predictions
      setSearchResults([]) // Reset search results
      setIsSearchResultShown(false) // Reset search result state
      setSearchValue('')
      setShowMoreSuggestions(false) // Reset ke bentuk tombol
      document.body.classList.remove('show-suggestions')
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      setShowPredictions(false) // Start with suggestions, not predictions
      setSearchResults([]) // Reset search results
      setIsSearchResultShown(false) // Reset search result state
      setShowMoreSuggestions(false) // Pastikan bentuk tombol saat expand
      document.body.classList.add('show-suggestions')
    }
  }



  // Database untuk prediksi keyword - sesuai docs/facet.html
  const keywordPredictionDB = {
    productKeywords: [
      "smartphone android", "sepatu sneakers", "tas selempang",
      "headphone bluetooth", "keyboard gaming", "power bank",
      "smart tv", "robot vacuum", "laptop gaming", "tablet android",
      "mouse wireless", "speaker bluetooth", "kamera mirrorless",
      "jam tangan pintar", "earphone wireless", "charger fast",
      "case handphone", "screen protector", "memory card",
      "hard disk external", "webcam hd", "microphone usb",
      "printer inkjet", "scanner document", "router wifi",
      "modem 4g", "flashdisk 32gb", "cd dvd blank",
      "kabel hdmi", "adaptor laptop", "cooling pad",
      "stand laptop", "tas laptop", "backpack anti theft",
      "tas sekolah", "tas ransel", "tas wanita", "tas pria",
      "sepatu running", "sepatu casual", "sepatu formal",
      "smartphone 5g", "smartphone murah", "smartphone gaming",
      "laptop asus", "laptop lenovo", "laptop acer", "laptop hp",
      "headphone gaming", "headphone studio", "headphone wireless",
      "keyboard mechanical", "keyboard wireless", "mouse gaming",
      "monitor gaming", "monitor 4k", "ssd 1tb", "ram 16gb"
    ],
    synonyms: {
      'hp': ['handphone', 'smartphone', 'ponsel', 'telepon'],
      'handphone': ['hp', 'smartphone', 'ponsel', 'telepon'],
      'smartphone': ['hp', 'handphone', 'ponsel', 'telepon'],
      'laptop': ['notebook', 'komputer', 'pc portable'],
      'sepatu': ['shoes', 'sneakers', 'footwear'],
      'tas': ['bag', 'ransel', 'tote', 'backpack'],
      'murah': ['ekonomis', 'terjangkau', 'hemat', 'diskon'],
      'bagus': ['berkualitas', 'terbaik', 'premium']
    },
    typoCorrections: {
      'handpone': 'handphone',
      'smartpone': 'smartphone',
      'blutooth': 'bluetooth',
      'hadphone': 'headphone',
      'headpone': 'headphone',
      'keybord': 'keyboard',
      'keyborad': 'keyboard',
      'smarthpone': 'smartphone',
      'smarphone': 'smartphone',
      'selempangan': 'selempang',
      'wireles': 'wireless',
      'wirelless': 'wireless'
    },
    relatedKeywords: {
      'smartphone': ['android', 'iphone', 'samsung', 'xiaomi'],
      'laptop': ['gaming', 'asus', 'lenovo', 'acer'],
      'sepatu': ['sneakers', 'running', 'casual', 'sport'],
      'tas': ['selempang', 'ransel', 'laptop', 'sekolah']
    },
    userInteractionHistory: []
  }

  // Sample products database - sesuai docs/facet.html
  const sampleProducts = [
    {
      id: 1,
      name: "Tas Sekolah Ransel Anti Air",
      shortName: "Tas Sekolah",
      category: "tas",
      price: "Rp 89.000",
      originalPrice: "Rp 120.000",
      discount: "26%",
      rating: "4.8",
      sold: "2rb+",
      shipping: "Gratis Ongkir",
      image: "https://images.unsplash.com/photo-**********-98eeb64c6a62?w=300&h=300&fit=crop",
      isMall: true,
      cod: true
    },
    {
      id: 2,
      name: "Smartphone Android 5G Gaming",
      shortName: "Smartphone 5G",
      category: "smartphone",
      price: "Rp 2.499.000",
      originalPrice: "Rp 3.000.000",
      discount: "17%",
      rating: "4.9",
      sold: "5rb+",
      shipping: "Gratis Ongkir",
      image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop",
      isMall: true,
      cod: false
    },
    {
      id: 3,
      name: "Sepatu Sneakers Running Pria",
      shortName: "Sepatu Running",
      category: "sepatu",
      price: "Rp 299.000",
      originalPrice: "Rp 450.000",
      discount: "34%",
      rating: "4.7",
      sold: "1rb+",
      shipping: "Gratis Ongkir",
      image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=300&fit=crop",
      isMall: false,
      cod: true
    },
    {
      id: 4,
      name: "Laptop Gaming ASUS ROG",
      shortName: "Laptop Gaming",
      category: "laptop",
      price: "Rp 12.999.000",
      originalPrice: "Rp 15.000.000",
      discount: "13%",
      rating: "4.9",
      sold: "500+",
      shipping: "Gratis Ongkir",
      image: "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=300&h=300&fit=crop",
      isMall: true,
      cod: false
    },
    {
      id: 5,
      name: "Headphone Bluetooth Wireless",
      shortName: "Headphone Bluetooth",
      category: "headphone",
      price: "Rp 199.000",
      originalPrice: "Rp 299.000",
      discount: "33%",
      rating: "4.6",
      sold: "3rb+",
      shipping: "Gratis Ongkir",
      image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
      isMall: false,
      cod: true
    },
    {
      id: 6,
      name: "Tas Selempang Pria Kulit",
      shortName: "Tas Selempang",
      category: "tas",
      price: "Rp 149.000",
      originalPrice: "Rp 200.000",
      discount: "26%",
      rating: "4.5",
      sold: "800+",
      shipping: "Gratis Ongkir",
      image: "https://images.unsplash.com/photo-**********-98eeb64c6a62?w=300&h=300&fit=crop",
      isMall: false,
      cod: true
    }
  ]

  // Function to generate predictions based on input - sesuai docs/facet.html
  const generatePredictions = (input: string) => {
    const inputLower = input.toLowerCase().trim()
    const words = inputLower.split(' ')

    // Menyimpan hasil berdasarkan kategori untuk memungkinkan duplikasi berdasarkan sumber
    let historyResults: any[] = []
    let productResults: any[] = []
    let relatedResults: any[] = []
    let synonymResults: any[] = []
    let correctionResults: any[] = []
    let trendingResults: any[] = []

    // 1. Tambahkan dari riwayat interaksi pengguna (maksimal 4)
    let historyCount = 0
    for (let i = 0; i < keywordPredictionDB.userInteractionHistory.length && historyCount < 4; i++) {
      const item = keywordPredictionDB.userInteractionHistory[i]
      if (item.toLowerCase().includes(inputLower)) {
        historyResults.push({
          text: item,
          type: 'history',
          relevance: calculateRelevance(item, inputLower, 'history')
        })
        historyCount++
      }
    }

    // 2. Tambahkan dari keyword produk
    keywordPredictionDB.productKeywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(inputLower)) {
        productResults.push({
          text: keyword,
          type: 'product',
          relevance: calculateRelevance(keyword, inputLower, 'product')
        })
      }
    })

    // 3. Tambahkan trending keywords (maksimal 5)
    const trendingKeywords = ['tas sekolah', 'smartphone 5g', 'laptop gaming', 'headphone wireless', 'sepatu running']
    trendingKeywords.forEach((keyword, index) => {
      if (keyword.toLowerCase().includes(inputLower)) {
        const baseRelevance = calculateRelevance(keyword, inputLower, 'trending')
        const positionBonus = (5 - index) * 5 // 25, 20, 15, 10, 5 poin tambahan
        trendingResults.push({
          text: keyword,
          type: 'trending',
          relevance: baseRelevance + positionBonus
        })
      }
    })

    // 4. Tambahkan dari related keywords
    words.forEach(word => {
      if (keywordPredictionDB.relatedKeywords[word]) {
        keywordPredictionDB.relatedKeywords[word].forEach(related => {
          relatedResults.push({
            text: related,
            type: 'related',
            relevance: calculateRelevance(related, inputLower, 'related')
          })
        })
      }
    })

    // 5. Tambahkan dari sinonim
    words.forEach(word => {
      if (keywordPredictionDB.synonyms[word]) {
        keywordPredictionDB.synonyms[word].forEach(synonym => {
          const newQuery = inputLower.replace(word, synonym)
          if (newQuery !== inputLower) {
            synonymResults.push({
              text: newQuery,
              type: 'synonym',
              relevance: calculateRelevance(newQuery, inputLower, 'synonym')
            })
          }
        })
      }
    })

    // 6. Periksa kemungkinan typo
    words.forEach(word => {
      if (keywordPredictionDB.typoCorrections[word]) {
        const corrected = inputLower.replace(word, keywordPredictionDB.typoCorrections[word])
        correctionResults.push({
          text: corrected,
          type: 'correction',
          relevance: calculateRelevance(corrected, inputLower, 'correction')
        })
      }
    })

    // Gabungkan semua hasil
    const allResults = [
      ...historyResults,
      ...productResults,
      ...trendingResults,
      ...relatedResults,
      ...synonymResults,
      ...correctionResults
    ]

    // Urutkan berdasarkan relevansi
    allResults.sort((a, b) => b.relevance - a.relevance)

    // Hapus duplikat berdasarkan text (tapi pertahankan yang relevance tertinggi)
    const uniqueResults: any[] = []
    const seenTexts = new Set()

    allResults.forEach(result => {
      if (!seenTexts.has(result.text.toLowerCase())) {
        seenTexts.add(result.text.toLowerCase())
        uniqueResults.push(result)
      }
    })

    // Batasi prediksi antara 4-12 item sesuai docs/facet.html
    const limitedPredictions = uniqueResults.slice(0, Math.max(4, Math.min(12, uniqueResults.length)))

    setPredictions(limitedPredictions)
  }

  // Function to calculate relevance score - sesuai docs/facet.html
  const calculateRelevance = (prediction: string, input: string, type: string) => {
    let score = 0

    // Bobot berdasarkan tipe - sesuai docs/facet.html
    const typeWeights = {
      'history': 100,
      'trending': 85,
      'product': 80,
      'correction': 75,
      'related': 60,
      'synonym': 50,
      'suggestion': 40
    }

    // Tambahkan bobot tipe
    score += typeWeights[type as keyof typeof typeWeights] || 0

    // Cari kata-kata dalam input
    const inputWords = input.toLowerCase().split(' ')
    const predictionWords = prediction.toLowerCase().split(' ')
    const predictionLower = prediction.toLowerCase()

    // Jika prediksi dimulai dengan input, tambahkan skor lebih tinggi
    if (predictionLower.startsWith(input.toLowerCase())) {
      score += 30
    }

    // Jika prediksi berisi input persis, tambahkan skor
    if (predictionLower.includes(input.toLowerCase())) {
      score += 20
    }

    // Bobot lebih tinggi untuk setiap kata dalam input yang ada dalam prediksi
    let matchingWords = 0
    inputWords.forEach(word => {
      if (word.length > 0) {
        // Cek jika ada kata dalam prediksi yang mengandung kata input ini
        const hasMatch = predictionWords.some(pw => pw.includes(word))
        if (hasMatch) {
          matchingWords++
          // Bobot lebih tinggi untuk kata yang lebih panjang
          score += word.length * 2 // Kata panjang lebih relevan
        }
      }
    })

    // Tambahkan skor berdasarkan persentase kata yang cocok
    score += (matchingWords / inputWords.length) * 25

    // Jika prediksi memiliki kata yang mengandung kata input paling panjang, beri bobot tinggi
    if (inputWords.length > 0) {
      // Urutkan kata input berdasarkan panjang (dari terpanjang)
      const sortedInputWords = [...inputWords].sort((a, b) => b.length - a.length)
      const longestWord = sortedInputWords[0]

      if (longestWord.length >= 2 && predictionLower.includes(longestWord)) {
        score += 40 // Bobot tinggi jika mengandung kata terpanjang
      }
    }

    // Skor lebih rendah untuk prediksi yang terlalu panjang
    if (prediction.length > input.length + 20) {
      score -= 10
    }

    return score
  }

  // Function to handle prediction click - sesuai docs/facet.html dengan auto search
  const handlePredictionClick = (prediction: any) => {
    setSearchValue(prediction.text)
    setShowPredictions(false)
    setShowSuggestions(false)

    // Add to user interaction history - sesuai docs/facet.html
    // Hindari duplikat dengan menghapus item yang sama
    const index = keywordPredictionDB.userInteractionHistory.indexOf(prediction.text)
    if (index !== -1) {
      keywordPredictionDB.userInteractionHistory.splice(index, 1)
    }

    // Tambahkan ke awal array
    keywordPredictionDB.userInteractionHistory.unshift(prediction.text)

    // Batasi jumlah item riwayat ke 20 sesuai docs/facet.html
    if (keywordPredictionDB.userInteractionHistory.length > 20) {
      keywordPredictionDB.userInteractionHistory.pop()
    }

    // Simpan ke localStorage jika tersedia
    try {
      localStorage.setItem('keywordPredictionHistory', JSON.stringify(keywordPredictionDB.userInteractionHistory))
    } catch (e) {
      console.log('Failed to save prediction history to localStorage', e)
    }

    // Jalankan pencarian otomatis - sesuai docs/facet.html
    executeSearch(prediction.text)

    console.log('Prediction clicked and search executed:', prediction.text)
  }

  // Function to execute search - sesuai docs/facet.html
  const executeSearch = (searchText?: string) => {
    const query = searchText || searchValue
    console.log('Melakukan pencarian untuk:', query)

    if (query.trim()) {
      // Sembunyikan prediksi dan suggestions
      setShowPredictions(false)
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')

      // Lakukan pencarian
      const results = enhancedSearch(query)
      console.log('Hasil pencarian:', results.length, 'produk ditemukan')

      // Set hasil pencarian
      setSearchResults(results)
      setIsSearchResultShown(true)

      // Tambahkan ke search history
      if (!searchHistory.includes(query)) {
        const newHistory = [query, ...searchHistory.slice(0, 9)] // Maksimal 10 item
        setSearchHistory(newHistory)
        localStorage.setItem('searchHistory', JSON.stringify(newHistory))
      }
    } else {
      // Jika pencarian kosong, reset ke tampilan awal
      setSearchResults([])
      setIsSearchResultShown(false)
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    }
  }

  // Function to perform enhanced search - sesuai docs/facet.html
  const enhancedSearch = (query: string) => {
    const searchTerms = query.toLowerCase().trim().split(' ')
    const results: any[] = []

    sampleProducts.forEach(product => {
      let score = 0

      // Cek nama produk
      const productName = product.name.toLowerCase()
      const productShortName = product.shortName.toLowerCase()
      const productCategory = product.category.toLowerCase()

      searchTerms.forEach(term => {
        // Exact match di nama produk (score tinggi)
        if (productName.includes(term)) {
          score += 10
        }

        // Exact match di nama pendek
        if (productShortName.includes(term)) {
          score += 8
        }

        // Exact match di kategori
        if (productCategory.includes(term)) {
          score += 6
        }

        // Partial match di awal kata
        const words = productName.split(' ')
        words.forEach(word => {
          if (word.startsWith(term)) {
            score += 5
          }
        })
      })

      // Jika ada score, tambahkan ke hasil
      if (score > 0) {
        results.push({ ...product, searchScore: score })
      }
    })

    // Urutkan berdasarkan score (tertinggi dulu)
    return results.sort((a, b) => b.searchScore - a.searchScore)
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Bisa ditambahkan logic untuk navigate atau search
    console.log('Suggestion clicked:', suggestion)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
      setShowPredictions(false)
      setShowSuggestions(false)
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Overlay untuk expanded mode */}
        {(showSuggestions || showPredictions) && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onToggleExpanded={handleToggleExpanded}
      />

      {/* Main Content */}
      <main className="pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>
          
          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Keyword Predictions Container - Muncul saat mengetik minimal 1 huruf */}
      {showPredictions && (
        <div className="keyword-predictions" onClick={(e) => e.stopPropagation()}>
          {predictions.map((prediction, index) => {
            // Determine icon based on prediction type
            let iconClass = 'fa-search'
            if (prediction.type === 'history') {
              iconClass = 'fa-history'
            } else if (prediction.type === 'product') {
              iconClass = 'fa-shopping-cart'
            } else if (prediction.type === 'trending') {
              iconClass = 'fa-arrow-trend-up'
            }

            // Check if prediction contains main keyword from input
            const containsMainKeyword = (input: string, predictionText: string) => {
              const inputWords = input.toLowerCase().trim().split(' ')
              const mainWord = inputWords[0]
              if (mainWord && mainWord.length >= 2) {
                return predictionText.toLowerCase().includes(mainWord)
              }
              return false
            }

            const isRelevant = containsMainKeyword(searchValue, prediction.text)

            // Create highlighted text
            const highlightText = (text: string, input: string) => {
              if (!input.trim()) return text

              try {
                // Escape special regex characters
                const escapedInput = input.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
                const regex = new RegExp(`(${escapedInput})`, 'gi')
                const parts = text.split(regex)

                return parts.map((part, i) =>
                  regex.test(part) ?
                    <span key={i} className="highlighted">{part}</span> :
                    part
                )
              } catch (e) {
                // Fallback if regex fails
                return text
              }
            }

            return (
              <div
                key={index}
                className="prediction-item"
                onClick={() => handlePredictionClick(prediction)}
              >
                <span className={`prediction-icon ${isRelevant ? 'matched' : ''}`}>
                  <i className={`fa ${iconClass}`}></i>
                </span>
                <span className="prediction-text">
                  {highlightText(prediction.text, searchValue)}
                </span>
              </div>
            )
          })}
        </div>
      )}

      {/* Suggestions Container - Persis seperti docs/facet.html */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          {/* Clear history option - sesuai facet.html */}
          <div className="clear-history" onClick={clearSearchHistory}>
            <i className="fas fa-trash-alt" style={{ marginRight: '8px', fontSize: '12px' }}></i>
            Hapus riwayat pencarian
          </div>

          {/* Keyword suggestions di bagian atas - SEBELUM klik "Lihat Lainnya" = bentuk tombol dengan icon */}
          {!showMoreSuggestions && searchHistory.length > 0 && (
            <div className="keyword-button-container">
              {getInitialKeywords().map((keyword, index) => (
                <div
                  key={index}
                  className="keyword-button"
                  onClick={() => handleSuggestionClick(keyword)}
                >
                  <span className="suggestion-icon">
                    <i className="fa fa-history"></i>
                  </span>
                  <span className="keyword-button-text">{keyword}</span>
                </div>
              ))}
            </div>
          )}

          {/* Show empty message if no search history */}
          {!showMoreSuggestions && searchHistory.length === 0 && (
            <div className="empty-history-message">
              <p>Belum ada riwayat pencarian</p>
            </div>
          )}

          {/* Keyword suggestions di bagian atas - SETELAH klik "Lihat Lainnya" = bentuk list */}
          {showMoreSuggestions && (
            <div className="main-keyword-suggestions-list">
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('sepatu sneakers')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">sepatu sneakers</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('tas wanita')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">tas wanita</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('smartphone android')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">smartphone android</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('headphone bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">headphone bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('keyboard gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">keyboard gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('mouse wireless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">mouse wireless</span>
              </div>

              {/* Extended suggestions - tambahan setelah expand */}
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('kamera mirrorless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">kamera mirrorless</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('jam tangan pintar')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">jam tangan pintar</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('speaker bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">speaker bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('laptop gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">laptop gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('power bank 20000mah')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">power bank 20000mah</span>
              </div>
            </div>
          )}

          {/* Tombol Lihat Lainnya untuk keyword suggestions di bagian atas */}
          <div className="main-see-more-container">
            <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
              {!showMoreSuggestions ? (
                <>
                  <i className="fas fa-plus-circle"></i>
                  <span>Lihat Lainnya</span>
                </>
              ) : (
                <>
                  <i className="fas fa-minus-circle"></i>
                  <span>Sembunyikan</span>
                </>
              )}
            </button>
          </div>

          {/* Sedang Trend section - sesuai docs/facet.html */}
          <div className="trending-section">
            <div className="trend-pill">
              Sedang Trend
              <div className="trend-pill-badge">5</div>
            </div>

            {/* Trending keywords - selalu dalam bentuk list dengan icon trend */}
            <div className="trending-keywords-list">
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas Sekolah')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas Sekolah</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas Selempang</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Handphone')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Handphone</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas Mata')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas Mata</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas</span>
              </div>
            </div>
          </div>

          {/* Produk Populer section - bentuk card dengan image dan judul */}
          <div className="product-suggestions">
            <div className="product-title">Produk Populer</div>
            <div className="product-grid">
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Samsung Galaxy')}
              >
                <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=150&h=150&fit=crop" alt="Samsung Galaxy" className="product-img" />
                <div className="simple-product-name">Samsung Galaxy</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Sneakers Pria')}
              >
                <img src="https://images.unsplash.com/photo-1549298916-b41d501d3772?w=150&h=150&fit=crop" alt="Sneakers Pria" className="product-img" />
                <div className="simple-product-name">Sneakers Pria</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <img src="https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop" alt="Tas Selempang" className="product-img" />
                <div className="simple-product-name">Tas Selempang</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Headphone Bluetooth')}
              >
                <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=150&h=150&fit=crop" alt="Headphone Bluetooth" className="product-img" />
                <div className="simple-product-name">Headphone Bluetooth</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Keyboard Gaming')}
              >
                <img src="https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop" alt="Keyboard Gaming" className="product-img" />
                <div className="simple-product-name">Keyboard Gaming</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Power Bank')}
              >
                <img src="https://images.unsplash.com/photo-*************-3d9c5b1b8b8e?w=150&h=150&fit=crop" alt="Power Bank" className="product-img" />
                <div className="simple-product-name">Power Bank</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Smart TV')}
              >
                <img src="https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop" alt="Smart TV" className="product-img" />
                <div className="simple-product-name">Smart TV</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <img src="https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop" alt="Tas Selempang" className="product-img" />
                <div className="simple-product-name">Tas Selempang</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search Results Container - sesuai docs/facet.html */}
      {isSearchResultShown && (
        <div className="search-results-container">
          <div className="search-results-header">
            <h3>Hasil Pencarian untuk "{searchValue}"</h3>
            <span className="results-count">{searchResults.length} produk ditemukan</span>
          </div>

          {searchResults.length === 0 ? (
            <div className="no-results">
              <div className="no-results-icon">
                <i className="fas fa-search"></i>
              </div>
              <h4>Produk tidak ditemukan</h4>
              <p>Coba kata kunci lain atau periksa ejaan Anda</p>
            </div>
          ) : (
            <div className="search-results-grid">
              {searchResults.map((product) => (
                <div key={product.id} className="search-result-card">
                  <img src={product.image} alt={product.name} className="result-product-img" />
                  <div className="result-product-info">
                    <div className="result-product-name">
                      {product.isMall && <span className="sellzio-mall-icon">Mall</span>}
                      {product.name}
                    </div>
                    <div className="result-product-rating-sold">
                      <div className="result-product-rating">
                        <i className="fa fa-star"></i>
                        {product.rating}
                      </div>
                      <div className="result-product-sold">Terjual {product.sold}</div>
                    </div>
                    <div className="result-product-shipping">
                      <i className="fa fa-truck"></i>
                      {product.shipping}
                    </div>
                    <div className="result-product-price-container">
                      <div className="result-product-price">{product.price}</div>
                      <div className="result-product-price-original">{product.originalPrice}</div>
                      <div className="result-product-discount">-{product.discount}</div>
                    </div>
                  </div>
                  {product.cod && (
                    <div className="result-cod-icon">
                      <i className="fa fa-money-bill"></i>
                      COD
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      </div>
    </>
  )
}

export default SellzioPage
