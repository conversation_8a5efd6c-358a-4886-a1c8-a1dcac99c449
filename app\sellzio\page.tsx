"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showPredictions, setShowPredictions] = useState(false)
  const [predictions, setPredictions] = useState<any[]>([])

  // Initialize search history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('searchHistory')
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory))
    } else {
      // Set default keywords jika tidak ada history
      const defaultKeywords = ['tas', 'divf', 'tas mata', 't', 'tas sekolah', 'tas selempang']
      setSearchHistory(defaultKeywords)
      localStorage.setItem('searchHistory', JSON.stringify(defaultKeywords))
    }
  }, [])

  // Function to clear search history
  const clearSearchHistory = () => {
    setSearchHistory([])
    localStorage.setItem('searchHistory', JSON.stringify([]))
    console.log("Riwayat pencarian dihapus")
  }

  // Function to get initial keywords (first 6)
  const getInitialKeywords = () => {
    return searchHistory.slice(0, 6)
  }

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // Jangan auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)

    // Show predictions when typing (minimal 1 character) and search is expanded
    if (value.trim().length >= 1 && isSearchExpanded) {
      setShowPredictions(true)
      setShowSuggestions(false) // Hide suggestions when predictions are shown
      generatePredictions(value)
      document.body.classList.add('show-suggestions') // Use same class for overlay effect
    } else if (value.trim() === '' && isSearchExpanded) {
      // Show suggestions when input is empty
      setShowPredictions(false)
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    } else {
      setShowPredictions(false)
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setShowPredictions(false) // Reset predictions
      setSearchValue('')
      setShowMoreSuggestions(false) // Reset ke bentuk tombol
      document.body.classList.remove('show-suggestions')
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      setShowPredictions(false) // Start with suggestions, not predictions
      setShowMoreSuggestions(false) // Pastikan bentuk tombol saat expand
      document.body.classList.add('show-suggestions')
    }
  }



  // Database untuk prediksi keyword - sesuai docs/facet.html
  const keywordPredictionDB = {
    productKeywords: [
      "smartphone android", "sepatu sneakers", "tas selempang",
      "headphone bluetooth", "keyboard gaming", "power bank",
      "smart tv", "robot vacuum", "laptop gaming", "tablet android",
      "mouse wireless", "speaker bluetooth", "kamera mirrorless",
      "jam tangan pintar", "earphone wireless", "charger fast",
      "case handphone", "screen protector", "memory card",
      "hard disk external", "webcam hd", "microphone usb",
      "printer inkjet", "scanner document", "router wifi",
      "modem 4g", "flashdisk 32gb", "cd dvd blank",
      "kabel hdmi", "adaptor laptop", "cooling pad",
      "stand laptop", "tas laptop", "backpack anti theft"
    ],
    synonyms: {
      'hp': ['handphone', 'smartphone', 'ponsel', 'telepon'],
      'handphone': ['hp', 'smartphone', 'ponsel', 'telepon'],
      'smartphone': ['hp', 'handphone', 'ponsel', 'telepon'],
      'laptop': ['notebook', 'komputer', 'pc portable'],
      'sepatu': ['shoes', 'sneakers', 'footwear'],
      'tas': ['bag', 'ransel', 'tote', 'backpack'],
      'murah': ['ekonomis', 'terjangkau', 'hemat', 'diskon'],
      'bagus': ['berkualitas', 'terbaik', 'premium']
    },
    typoCorrections: {
      'handpone': 'handphone',
      'smartpone': 'smartphone',
      'blutooth': 'bluetooth',
      'hadphone': 'headphone',
      'headpone': 'headphone',
      'keybord': 'keyboard',
      'keyborad': 'keyboard',
      'smarthpone': 'smartphone',
      'smarphone': 'smartphone',
      'selempangan': 'selempang',
      'wireles': 'wireless',
      'wirelless': 'wireless'
    },
    relatedKeywords: {
      'smartphone': ['android', 'iphone', 'samsung', 'xiaomi'],
      'laptop': ['gaming', 'asus', 'lenovo', 'acer'],
      'sepatu': ['sneakers', 'running', 'casual', 'sport'],
      'tas': ['selempang', 'ransel', 'laptop', 'sekolah']
    },
    userInteractionHistory: []
  }

  // Function to generate predictions based on input
  const generatePredictions = (input: string) => {
    const inputLower = input.toLowerCase().trim()
    const results: any[] = []

    // 1. Add from product keywords
    keywordPredictionDB.productKeywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(inputLower)) {
        results.push({
          text: keyword,
          type: 'product',
          relevance: calculateRelevance(keyword, inputLower, 'product')
        })
      }
    })

    // 2. Add from user interaction history
    keywordPredictionDB.userInteractionHistory.forEach(item => {
      if (item.toLowerCase().includes(inputLower)) {
        results.push({
          text: item,
          type: 'history',
          relevance: calculateRelevance(item, inputLower, 'history')
        })
      }
    })

    // 3. Add trending keywords
    const trendingKeywords = ['tas sekolah', 'smartphone 5g', 'laptop gaming', 'headphone wireless', 'sepatu running']
    trendingKeywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(inputLower)) {
        results.push({
          text: keyword,
          type: 'trending',
          relevance: calculateRelevance(keyword, inputLower, 'trending') + 10
        })
      }
    })

    // Sort by relevance and limit to 8-12 items
    const sortedResults = results
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, Math.max(4, Math.min(12, results.length)))

    setPredictions(sortedResults)
  }

  // Function to calculate relevance score
  const calculateRelevance = (prediction: string, input: string, type: string) => {
    let score = 0
    const predictionLower = prediction.toLowerCase()
    const inputLower = input.toLowerCase()

    // Exact match at start gets highest score
    if (predictionLower.startsWith(inputLower)) {
      score += 50
    }

    // Contains input gets medium score
    if (predictionLower.includes(inputLower)) {
      score += 30
    }

    // Type-based scoring
    if (type === 'history') score += 20
    if (type === 'trending') score += 15
    if (type === 'product') score += 10

    return score
  }

  // Function to handle prediction click
  const handlePredictionClick = (prediction: any) => {
    setSearchValue(prediction.text)
    setShowPredictions(false)

    // Add to user interaction history
    if (!keywordPredictionDB.userInteractionHistory.includes(prediction.text)) {
      keywordPredictionDB.userInteractionHistory.unshift(prediction.text)
      // Limit history to 20 items
      if (keywordPredictionDB.userInteractionHistory.length > 20) {
        keywordPredictionDB.userInteractionHistory.pop()
      }
    }

    console.log('Prediction clicked:', prediction.text)
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Bisa ditambahkan logic untuk navigate atau search
    console.log('Suggestion clicked:', suggestion)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
      setShowPredictions(false)
      setShowSuggestions(false)
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Overlay untuk expanded mode */}
        {(showSuggestions || showPredictions) && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onToggleExpanded={handleToggleExpanded}
      />

      {/* Main Content */}
      <main className="pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>
          
          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Keyword Predictions Container - Muncul saat mengetik minimal 1 huruf */}
      {showPredictions && (
        <div className="keyword-predictions" onClick={(e) => e.stopPropagation()}>
          {predictions.map((prediction, index) => {
            // Determine icon based on prediction type
            let iconClass = 'fa-search'
            if (prediction.type === 'history') {
              iconClass = 'fa-history'
            } else if (prediction.type === 'product') {
              iconClass = 'fa-shopping-cart'
            } else if (prediction.type === 'trending') {
              iconClass = 'fa-arrow-trend-up'
            }

            // Check if prediction contains main keyword from input
            const containsMainKeyword = (input: string, predictionText: string) => {
              const inputWords = input.toLowerCase().trim().split(' ')
              const mainWord = inputWords[0]
              if (mainWord && mainWord.length >= 2) {
                return predictionText.toLowerCase().includes(mainWord)
              }
              return false
            }

            const isRelevant = containsMainKeyword(searchValue, prediction.text)

            // Create highlighted text
            const highlightText = (text: string, input: string) => {
              if (!input.trim()) return text

              try {
                // Escape special regex characters
                const escapedInput = input.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
                const regex = new RegExp(`(${escapedInput})`, 'gi')
                const parts = text.split(regex)

                return parts.map((part, i) =>
                  regex.test(part) ?
                    <span key={i} className="highlighted">{part}</span> :
                    part
                )
              } catch (e) {
                // Fallback if regex fails
                return text
              }
            }

            return (
              <div
                key={index}
                className="prediction-item"
                onClick={() => handlePredictionClick(prediction)}
              >
                <span className={`prediction-icon ${isRelevant ? 'matched' : ''}`}>
                  <i className={`fa ${iconClass}`}></i>
                </span>
                <span className="prediction-text">
                  {highlightText(prediction.text, searchValue)}
                </span>
              </div>
            )
          })}
        </div>
      )}

      {/* Suggestions Container - Persis seperti docs/facet.html */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          {/* Clear history option - sesuai facet.html */}
          <div className="clear-history" onClick={clearSearchHistory}>
            <i className="fas fa-trash-alt" style={{ marginRight: '8px', fontSize: '12px' }}></i>
            Hapus riwayat pencarian
          </div>

          {/* Keyword suggestions di bagian atas - SEBELUM klik "Lihat Lainnya" = bentuk tombol dengan icon */}
          {!showMoreSuggestions && searchHistory.length > 0 && (
            <div className="keyword-button-container">
              {getInitialKeywords().map((keyword, index) => (
                <div
                  key={index}
                  className="keyword-button"
                  onClick={() => handleSuggestionClick(keyword)}
                >
                  <span className="suggestion-icon">
                    <i className="fa fa-history"></i>
                  </span>
                  <span className="keyword-button-text">{keyword}</span>
                </div>
              ))}
            </div>
          )}

          {/* Show empty message if no search history */}
          {!showMoreSuggestions && searchHistory.length === 0 && (
            <div className="empty-history-message">
              <p>Belum ada riwayat pencarian</p>
            </div>
          )}

          {/* Keyword suggestions di bagian atas - SETELAH klik "Lihat Lainnya" = bentuk list */}
          {showMoreSuggestions && (
            <div className="main-keyword-suggestions-list">
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('sepatu sneakers')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">sepatu sneakers</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('tas wanita')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">tas wanita</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('smartphone android')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">smartphone android</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('headphone bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">headphone bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('keyboard gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">keyboard gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('mouse wireless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">mouse wireless</span>
              </div>

              {/* Extended suggestions - tambahan setelah expand */}
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('kamera mirrorless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">kamera mirrorless</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('jam tangan pintar')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">jam tangan pintar</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('speaker bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">speaker bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('laptop gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">laptop gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('power bank 20000mah')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">power bank 20000mah</span>
              </div>
            </div>
          )}

          {/* Tombol Lihat Lainnya untuk keyword suggestions di bagian atas */}
          <div className="main-see-more-container">
            <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
              {!showMoreSuggestions ? (
                <>
                  <i className="fas fa-plus-circle"></i>
                  <span>Lihat Lainnya</span>
                </>
              ) : (
                <>
                  <i className="fas fa-minus-circle"></i>
                  <span>Sembunyikan</span>
                </>
              )}
            </button>
          </div>

          {/* Sedang Trend section - sesuai docs/facet.html */}
          <div className="trending-section">
            <div className="trend-pill">
              Sedang Trend
              <div className="trend-pill-badge">5</div>
            </div>

            {/* Trending keywords - selalu dalam bentuk list dengan icon trend */}
            <div className="trending-keywords-list">
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas Sekolah')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas Sekolah</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas Selempang</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Handphone')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Handphone</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas Mata')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas Mata</span>
              </div>
              <div
                className="suggestion-item trending-item"
                onClick={() => handleSuggestionClick('Tas')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-arrow-trend-up"></i>
                </div>
                <span className="suggestion-text">Tas</span>
              </div>
            </div>
          </div>

          {/* Produk Populer section - bentuk card dengan image dan judul */}
          <div className="product-suggestions">
            <div className="product-title">Produk Populer</div>
            <div className="product-grid">
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Samsung Galaxy')}
              >
                <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=150&h=150&fit=crop" alt="Samsung Galaxy" className="product-img" />
                <div className="simple-product-name">Samsung Galaxy</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Sneakers Pria')}
              >
                <img src="https://images.unsplash.com/photo-1549298916-b41d501d3772?w=150&h=150&fit=crop" alt="Sneakers Pria" className="product-img" />
                <div className="simple-product-name">Sneakers Pria</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <img src="https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop" alt="Tas Selempang" className="product-img" />
                <div className="simple-product-name">Tas Selempang</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Headphone Bluetooth')}
              >
                <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=150&h=150&fit=crop" alt="Headphone Bluetooth" className="product-img" />
                <div className="simple-product-name">Headphone Bluetooth</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Keyboard Gaming')}
              >
                <img src="https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop" alt="Keyboard Gaming" className="product-img" />
                <div className="simple-product-name">Keyboard Gaming</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Power Bank')}
              >
                <img src="https://images.unsplash.com/photo-*************-3d9c5b1b8b8e?w=150&h=150&fit=crop" alt="Power Bank" className="product-img" />
                <div className="simple-product-name">Power Bank</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Smart TV')}
              >
                <img src="https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop" alt="Smart TV" className="product-img" />
                <div className="simple-product-name">Smart TV</div>
              </div>
              <div
                className="simple-product-card"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <img src="https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop" alt="Tas Selempang" className="product-img" />
                <div className="simple-product-name">Tas Selempang</div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}

export default SellzioPage
